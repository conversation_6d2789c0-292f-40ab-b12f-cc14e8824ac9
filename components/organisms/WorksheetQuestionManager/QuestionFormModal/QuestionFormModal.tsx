'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { X, Plus, Trash2, Loader2 } from 'lucide-react';
import { IWorksheetQuestion, IAddQuestionToWorksheetDto, IUpdateWorksheetQuestionDto } from '@/apis/worksheetQuestionApi';
import { handleCreateQuestionAction, handleUpdateQuestionAction } from '@/actions/worksheetQuestion.action';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface QuestionFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (question: IWorksheetQuestion) => void;
  worksheetId: string;
  questionTypes: Array<{ value: string; label: string }>;
  mode: 'create' | 'edit';
  initialData?: IWorksheetQuestion;
  className?: string;
}

interface QuestionFormData {
  type: string;
  content: string;
  options: Array<{ value: string }>;
  answer: string[];
  explain: string;
  points: number;
  subject?: string;
  grade?: string;
  difficulty?: string;
  imageUrl?: string;
  imagePrompt?: string;
}

export const QuestionFormModal: React.FC<QuestionFormModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  worksheetId,
  questionTypes,
  mode,
  initialData,
  className
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dialogRef = useRef<HTMLDialogElement>(null);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<QuestionFormData>({
    defaultValues: {
      type: '',
      content: '',
      options: [{ value: '' }, { value: '' }],
      answer: [],
      explain: '',
      points: 1,
      subject: '',
      grade: '',
      difficulty: 'MEDIUM',
      imageUrl: '',
      imagePrompt: ''
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'options'
  });

  const watchedType = watch('type');
  const watchedOptions = watch('options');

  // Handle dialog open/close with proper DaisyUI pattern
  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
    } else {
      dialog.close();
    }
  }, [isOpen]);

  // Initialize form with existing data when editing
  useEffect(() => {
    if (mode === 'edit' && initialData) {
      reset({
        type: initialData.type,
        content: initialData.content,
        options: initialData.options?.map(opt => ({ value: opt })) || [{ value: '' }, { value: '' }],
        answer: initialData.answer || [],
        explain: initialData.explain,
        points: initialData.points || 1,
        subject: initialData.subject || '',
        grade: initialData.grade || '',
        difficulty: initialData.difficulty || 'MEDIUM',
        imageUrl: initialData.imageUrl || '',
        imagePrompt: initialData.imagePrompt || ''
      });
    } else {
      reset({
        type: '',
        content: '',
        options: [{ value: '' }, { value: '' }],
        answer: [],
        explain: '',
        points: 1,
        subject: '',
        grade: '',
        difficulty: 'MEDIUM',
        imageUrl: '',
        imagePrompt: ''
      });
    }
  }, [mode, initialData, reset]);

  const onSubmit = async (data: QuestionFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const questionData = {
        type: data.type as any,
        content: data.content,
        options: data.options.map(opt => opt.value).filter(Boolean),
        answer: data.answer,
        explain: data.explain,
        points: data.points,
        subject: data.subject,
        grade: data.grade,
        difficulty: data.difficulty as any,
        imageUrl: data.imageUrl,
        imagePrompt: data.imagePrompt
      };

      let response;
      if (mode === 'create') {
        response = await handleCreateQuestionAction(worksheetId, questionData as IAddQuestionToWorksheetDto);
      } else {
        response = await handleUpdateQuestionAction(
          worksheetId,
          initialData!.id,
          questionData as IUpdateWorksheetQuestionDto
        );
      }

      if (response.status === 'success') {
        // Create a mock question object for the success callback
        const updatedQuestion: IWorksheetQuestion = {
          id: initialData?.id || 'new-question',
          ...questionData,
          position: initialData?.position || 1,
          createdAt: initialData?.createdAt || new Date(),
          updatedAt: new Date(),
          version: (initialData?.version || 0) + 1
        };
        
        onSuccess(updatedQuestion);
        handleClose();
      } else {
        setError(response.message || 'Failed to save question');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addOption = () => {
    append({ value: '' });
  };

  const removeOption = (index: number) => {
    if (fields.length > 2) {
      remove(index);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setError(null);
      reset();
      onClose();
    }
  };

  const isMultipleChoice = watchedType === 'MULTIPLE_CHOICE';
  const isTrueFalse = watchedType === 'TRUE_FALSE';

  // Auto-set options for True/False questions
  useEffect(() => {
    if (isTrueFalse) {
      setValue('options', [{ value: 'True' }, { value: 'False' }]);
    }
  }, [isTrueFalse, setValue]);

  return (
    <dialog
      ref={dialogRef}
      className={cn("modal modal-bottom sm:modal-middle", className)}
      onClose={handleClose}
    >
      <div className="modal-box w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">
            {mode === 'create' ? 'Add New Question' : 'Edit Question'}
          </h3>
          <form method="dialog">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-ghost btn-sm btn-circle"
              disabled={isSubmitting}
            >
              <X className="w-4 h-4" />
            </button>
          </form>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-error mb-4">
            <span>{error}</span>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Question Type */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Question Type *</span>
            </label>
            <select
              {...register('type', { required: 'Question type is required' })}
              className="select select-bordered w-full"
              disabled={isSubmitting}
            >
              <option value="">Select question type</option>
              {questionTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {errors.type && (
              <label className="label">
                <span className="label-text-alt text-error">{errors.type.message}</span>
              </label>
            )}
          </div>

          {/* Question Content */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Question Content *</span>
            </label>
            <textarea
              {...register('content', { required: 'Question content is required' })}
              className="textarea textarea-bordered h-24"
              placeholder="Enter your question here..."
              disabled={isSubmitting}
            />
            {errors.content && (
              <label className="label">
                <span className="label-text-alt text-error">{errors.content.message}</span>
              </label>
            )}
          </div>

          {/* Answer Options */}
          {watchedType && !['SHORT_ANSWER', 'ESSAY'].includes(watchedType) && (
            <div className="form-control">
              <label className="label">
                <span className="label-text">Answer Options *</span>
              </label>
              <div className="space-y-2">
                {fields.map((field, index) => (
                  <div key={field.id} className="flex items-center gap-2">
                    <input
                      {...register(`options.${index}.value`, { required: 'Option cannot be empty' })}
                      className="input input-bordered flex-1"
                      placeholder={`Option ${index + 1}`}
                      disabled={isSubmitting || isTrueFalse}
                    />
                    {!isTrueFalse && fields.length > 2 && (
                      <button
                        type="button"
                        onClick={() => removeOption(index)}
                        className="btn btn-ghost btn-sm btn-circle text-error"
                        disabled={isSubmitting}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
                {!isTrueFalse && (
                  <button
                    type="button"
                    onClick={addOption}
                    className="btn btn-ghost btn-sm"
                    disabled={isSubmitting || fields.length >= 10}
                  >
                    <Plus className="w-4 h-4" />
                    Add Option
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Correct Answers */}
          {watchedType && !['SHORT_ANSWER', 'ESSAY'].includes(watchedType) && (
            <div className="form-control">
              <label className="label">
                <span className="label-text">Correct Answer(s) *</span>
              </label>
              <div className="space-y-2">
                {watchedOptions.map((option, index) => (
                  option.value && (
                    <label key={index} className="label cursor-pointer justify-start gap-2">
                      <input
                        type={isMultipleChoice ? "checkbox" : "radio"}
                        {...register('answer', { required: 'At least one correct answer is required' })}
                        value={option.value}
                        className={isMultipleChoice ? "checkbox checkbox-primary" : "radio radio-primary"}
                        disabled={isSubmitting}
                      />
                      <span className="label-text">{option.value}</span>
                    </label>
                  )
                ))}
              </div>
              {errors.answer && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.answer.message}</span>
                </label>
              )}
            </div>
          )}

          {/* Explanation */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Answer Explanation *</span>
            </label>
            <textarea
              {...register('explain', { required: 'Answer explanation is required' })}
              className="textarea textarea-bordered h-20"
              placeholder="Explain why this is the correct answer..."
              disabled={isSubmitting}
            />
            {errors.explain && (
              <label className="label">
                <span className="label-text-alt text-error">{errors.explain.message}</span>
              </label>
            )}
          </div>

          {/* Additional Fields Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Points */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Points</span>
              </label>
              <input
                type="number"
                {...register('points', { min: 0, max: 100 })}
                className="input input-bordered"
                min="0"
                max="100"
                disabled={isSubmitting}
              />
            </div>

            {/* Difficulty */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Difficulty</span>
              </label>
              <select
                {...register('difficulty')}
                className="select select-bordered"
                disabled={isSubmitting}
              >
                <option value="EASY">Easy</option>
                <option value="MEDIUM">Medium</option>
                <option value="HARD">Hard</option>
              </select>
            </div>

            {/* Subject */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Subject</span>
              </label>
              <input
                {...register('subject')}
                className="input input-bordered"
                placeholder="e.g., Mathematics"
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isSubmitting}
              className="btn-primary"
            >
              {isSubmitting && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
              {mode === 'create' ? 'Add Question' : 'Update Question'}
            </Button>
          </div>
        </form>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button type="button" onClick={handleClose}>close</button>
      </form>
    </dialog>
  );
};
