'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { X, Plus, Trash2, Loader2 } from 'lucide-react';
import { IWorksheetQuestion, IAddQuestionToWorksheetDto, IUpdateWorksheetQuestionDto } from '@/apis/worksheetQuestionApi';
import { handleCreateQuestionAction, handleUpdateQuestionAction } from '@/actions/worksheetQuestion.action';
import { Button } from '@/components/atoms/Button/Button';
import { RichTextEditor } from '@/components/atoms/RichTextEditor/RichTextEditor';
import { cn } from '@/utils/cn';

export interface QuestionFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (question: IWorksheetQuestion) => void;
  worksheetId: string;
  questionTypes: Array<{ value: string; label: string }>;
  mode: 'create' | 'edit';
  initialData?: IWorksheetQuestion;
  className?: string;
}

interface QuestionFormData {
  type: string;
  content: string;
  options: Array<{ value: string }>;
  answer: string[];
  explain: string;
  points: number;
  subject?: string;
  grade?: string;
  difficulty?: string;
  imageUrl?: string;
  imagePrompt?: string;
}

export const QuestionFormModal: React.FC<QuestionFormModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  worksheetId,
  questionTypes,
  mode,
  initialData,
  className
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [questionContent, setQuestionContent] = useState('');
  const [answerExplanation, setAnswerExplanation] = useState('');
  const dialogRef = useRef<HTMLDialogElement>(null);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<QuestionFormData>({
    defaultValues: {
      type: '',
      content: '',
      options: [{ value: '' }, { value: '' }],
      answer: [],
      explain: '',
      points: 1,
      subject: '',
      grade: '',
      difficulty: 'MEDIUM',
      imageUrl: '',
      imagePrompt: ''
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'options'
  });

  const watchedType = watch('type');
  const watchedOptions = watch('options');
  const watchedAnswers = watch('answer');

  // Handle dialog open/close with proper DaisyUI pattern
  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
    } else {
      dialog.close();
    }
  }, [isOpen]);

  // Initialize form with existing data when editing
  useEffect(() => {
    if (mode === 'edit' && initialData) {
      reset({
        type: initialData.type,
        content: initialData.content,
        options: initialData.options?.map(opt => ({ value: opt })) || [{ value: '' }, { value: '' }],
        answer: initialData.answer || [],
        explain: initialData.explain,
        points: initialData.points || 1,
        subject: initialData.subject || '',
        grade: initialData.grade || '',
        difficulty: initialData.difficulty || 'MEDIUM',
        imageUrl: initialData.imageUrl || '',
        imagePrompt: initialData.imagePrompt || ''
      });
      setQuestionContent(initialData.content || '');
      setAnswerExplanation(initialData.explain || '');
    } else {
      reset({
        type: '',
        content: '',
        options: [{ value: '' }, { value: '' }],
        answer: [],
        explain: '',
        points: 1,
        subject: '',
        grade: '',
        difficulty: 'MEDIUM',
        imageUrl: '',
        imagePrompt: ''
      });
      setQuestionContent('');
      setAnswerExplanation('');
    }
  }, [mode, initialData, reset]);

  const onSubmit = async (data: QuestionFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const questionData = {
        type: data.type as any,
        content: questionContent || data.content,
        options: data.options.map(opt => opt.value).filter(Boolean),
        answer: data.answer,
        explain: answerExplanation || data.explain,
        points: data.points,
        subject: data.subject,
        grade: data.grade,
        difficulty: data.difficulty as any,
        imageUrl: data.imageUrl,
        imagePrompt: data.imagePrompt
      };

      let response;
      if (mode === 'create') {
        response = await handleCreateQuestionAction(worksheetId, questionData as IAddQuestionToWorksheetDto);
      } else {
        response = await handleUpdateQuestionAction(
          worksheetId,
          initialData!.id,
          questionData as IUpdateWorksheetQuestionDto
        );
      }

      if (response.status === 'success') {
        // Create a mock question object for the success callback
        const updatedQuestion: IWorksheetQuestion = {
          id: initialData?.id || 'new-question',
          ...questionData,
          position: initialData?.position || 1,
          createdAt: initialData?.createdAt || new Date(),
          updatedAt: new Date(),
          version: (initialData?.version || 0) + 1
        };
        
        onSuccess(updatedQuestion);
        handleClose();
      } else {
        setError(response.message || 'Failed to save question');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addOption = () => {
    append({ value: '' });
  };

  const removeOption = (index: number) => {
    if (fields.length > 2) {
      remove(index);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setError(null);
      reset();
      onClose();
    }
  };

  const isMultipleChoice = watchedType === 'MULTIPLE_CHOICE';
  const isTrueFalse = watchedType === 'TRUE_FALSE';

  // Auto-set options for True/False questions
  useEffect(() => {
    if (isTrueFalse) {
      setValue('options', [{ value: 'True' }, { value: 'False' }]);
    }
  }, [isTrueFalse, setValue]);

  return (
    <dialog
      ref={dialogRef}
      className={cn("modal modal-bottom sm:modal-middle", className)}
      onClose={handleClose}
    >
      <div className="modal-box w-full max-w-6xl max-h-[95vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            {mode === 'create' ? 'Add New Question' : 'Edit Question'}
          </h3>
          <form method="dialog">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-ghost btn-sm btn-circle"
              disabled={isSubmitting}
            >
              <X className="w-4 h-4" />
            </button>
          </form>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-error mb-3">
            <span>{error}</span>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
          {/* Question Type and Points Row */}
          <div className="bg-base-50 p-4 rounded-lg border border-base-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="form-control">
                <label className="label py-1">
                  <span className="label-text font-medium">Question Type</span>
                </label>
                {mode === 'edit' ? (
                  <div className="input input-bordered input-sm bg-base-100 flex items-center text-base-content/70">
                    {questionTypes.find(type => type.value === (initialData?.type || watch('type')))?.label || 'Unknown Type'}
                  </div>
                ) : (
                  <>
                    <select
                      {...register('type', { required: 'Question type is required' })}
                      className="select select-bordered select-sm w-full"
                      disabled={isSubmitting}
                    >
                      <option value="">Select question type</option>
                      {questionTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                    {errors.type && (
                      <label className="label py-0">
                        <span className="label-text-alt text-error text-xs">{errors.type.message}</span>
                      </label>
                    )}
                  </>
                )}
              </div>

              <div className="form-control">
                <label className="label py-1">
                  <span className="label-text font-medium">Points</span>
                </label>
                <input
                  type="number"
                  {...register('points', { min: 0, max: 100 })}
                  className="input input-bordered input-sm"
                  min="0"
                  max="100"
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </div>

          {/* Question Content */}
          <div className="form-control">
            <label className="label py-1">
              <span className="label-text font-medium">Question Content *</span>
            </label>
            <RichTextEditor
              value={questionContent}
              onChange={(value) => {
                setQuestionContent(value);
                setValue('content', value);
              }}
              placeholder="Enter your question here..."
              height="100px"
              readOnly={isSubmitting}
              className="w-full"
            />
            {(!questionContent || questionContent.trim() === '') && (
              <label className="label py-0">
                <span className="label-text-alt text-error text-xs">Question content is required</span>
              </label>
            )}
          </div>

          {/* Answer Options */}
          {watchedType && !['SHORT_ANSWER', 'ESSAY'].includes(watchedType) && (
            <div className="form-control">
              <label className="label py-1">
                <span className="label-text font-medium">Answer Options *</span>
              </label>
              <div className="bg-base-50 p-4 rounded-lg border border-base-200 space-y-2">
                {fields.map((field, index) => (
                  <div key={field.id} className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-primary">{String.fromCharCode(65 + index)}</span>
                    </div>
                    <input
                      {...register(`options.${index}.value`, { required: 'Option cannot be empty' })}
                      className="input input-bordered input-sm flex-1 bg-white"
                      placeholder={`Enter option ${String.fromCharCode(65 + index)}`}
                      disabled={isSubmitting || isTrueFalse}
                    />
                    {!isTrueFalse && fields.length > 2 && (
                      <button
                        type="button"
                        onClick={() => removeOption(index)}
                        className="btn btn-ghost btn-xs btn-circle text-error hover:bg-error/10"
                        disabled={isSubmitting}
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                ))}
                {!isTrueFalse && (
                  <div className="pt-2 border-t border-base-200">
                    <button
                      type="button"
                      onClick={addOption}
                      className="btn btn-ghost btn-xs text-primary hover:bg-primary/10"
                      disabled={isSubmitting || fields.length >= 10}
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Add Option
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Correct Answers */}
          {watchedType && !['SHORT_ANSWER', 'ESSAY'].includes(watchedType) && (
            <div className="form-control">
              <label className="label py-1">
                <span className="label-text font-medium">Correct Answer(s) *</span>
                <span className="label-text-alt text-xs text-base-content/60">
                  {isMultipleChoice ? 'Select all correct answers' : 'Select one correct answer'}
                </span>
              </label>
              <div className="bg-success/5 p-4 rounded-lg border border-success/20">
                <div className="grid grid-cols-1 gap-2">
                  {watchedOptions.map((option, index) => (
                    option.value && (
                      <label key={index} className="flex items-center gap-3 p-2 rounded-md hover:bg-success/10 cursor-pointer transition-colors">
                        <div className="flex-shrink-0 w-6 h-6 bg-success/10 rounded-full flex items-center justify-center">
                          <span className="text-xs font-medium text-success">{String.fromCharCode(65 + index)}</span>
                        </div>
                        <input
                          type={isMultipleChoice ? "checkbox" : "radio"}
                          {...register('answer', { required: 'At least one correct answer is required' })}
                          value={option.value}
                          checked={isMultipleChoice
                            ? Array.isArray(watchedAnswers) && watchedAnswers.includes(option.value)
                            : Array.isArray(watchedAnswers) ? watchedAnswers[0] === option.value : watchedAnswers === option.value
                          }
                          className={isMultipleChoice ? "checkbox checkbox-success checkbox-sm" : "radio radio-success radio-sm"}
                          disabled={isSubmitting}
                        />
                        <span className="text-sm flex-1">{option.value}</span>
                      </label>
                    )
                  ))}
                </div>
              </div>
              {errors.answer && (
                <label className="label py-0">
                  <span className="label-text-alt text-error text-xs">{errors.answer.message}</span>
                </label>
              )}
            </div>
          )}

          {/* Explanation */}
          <div className="form-control">
            <label className="label py-1">
              <span className="label-text font-medium">Answer Explanation *</span>
            </label>
            <RichTextEditor
              value={answerExplanation}
              onChange={(value) => {
                setAnswerExplanation(value);
                setValue('explain', value);
              }}
              placeholder="Explain why this is the correct answer..."
              height="90px"
              readOnly={isSubmitting}
              className="w-full"
            />
            {(!answerExplanation || answerExplanation.trim() === '') && (
              <label className="label py-0">
                <span className="label-text-alt text-error text-xs">Answer explanation is required</span>
              </label>
            )}
          </div>

          {/* Additional Fields Row */}
          <div className="bg-base-50 p-4 rounded-lg border border-base-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Difficulty */}
              <div className="form-control">
                <label className="label py-1">
                  <span className="label-text font-medium">Difficulty</span>
                </label>
                <select
                  {...register('difficulty')}
                  className="select select-bordered select-sm bg-white"
                  disabled={isSubmitting}
                >
                  <option value="EASY">🟢 Easy</option>
                  <option value="MEDIUM">🟡 Medium</option>
                  <option value="HARD">🔴 Hard</option>
                </select>
              </div>

              {/* Subject */}
              <div className="form-control">
                <label className="label py-1">
                  <span className="label-text font-medium">Subject</span>
                </label>
                <input
                  {...register('subject')}
                  className="input input-bordered input-sm bg-white"
                  placeholder="e.g., Mathematics"
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-base-200 bg-base-50 -mx-6 -mb-6 px-6 pb-6 rounded-b-lg">
            <Button
              type="button"
              variant="ghost"
              onClick={handleClose}
              disabled={isSubmitting}
              className="btn-sm min-w-20"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isSubmitting || (!questionContent?.trim()) || (!answerExplanation?.trim())}
              className="btn-primary btn-sm min-w-32"
            >
              {isSubmitting && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
              {mode === 'create' ? 'Add Question' : 'Update Question'}
            </Button>
          </div>
        </form>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button type="button" onClick={handleClose}>close</button>
      </form>
    </dialog>
  );
};
