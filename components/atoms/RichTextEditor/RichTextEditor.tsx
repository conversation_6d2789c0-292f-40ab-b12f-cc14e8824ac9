'use client';

import React, { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { cn } from '@/utils/cn';

// Dynamic import for Quill to avoid SSR issues
let Quill: any = null;
if (typeof window !== 'undefined') {
  import('quill').then((quillModule) => {
    Quill = quillModule.default;
  });
}

export interface RichTextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  height?: string;
  readOnly?: boolean;
  theme?: 'snow' | 'bubble';
  modules?: any;
  formats?: string[];
}

export interface RichTextEditorRef {
  getEditor: () => any;
  focus: () => void;
  blur: () => void;
}

export const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(({
  value = '',
  onChange,
  placeholder = 'Enter text...',
  className,
  height = '120px',
  readOnly = false,
  theme = 'snow',
  modules,
  formats
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const quillRef = useRef<any>(null);
  const isUpdatingRef = useRef(false);

  // Default modules for compact editor
  const defaultModules = {
    toolbar: [
      ['bold', 'italic', 'underline'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link'],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    }
  };

  // Default formats
  const defaultFormats = [
    'bold', 'italic', 'underline',
    'list', 'bullet',
    'link',
    'script'
  ];

  useImperativeHandle(ref, () => ({
    getEditor: () => quillRef.current,
    focus: () => quillRef.current?.focus(),
    blur: () => quillRef.current?.blur(),
  }));

  useEffect(() => {
    if (!Quill || !editorRef.current || quillRef.current) return;

    // Initialize Quill
    quillRef.current = new Quill(editorRef.current, {
      theme,
      readOnly,
      placeholder,
      modules: modules || defaultModules,
      formats: formats || defaultFormats,
    });

    // Set initial content
    if (value) {
      quillRef.current.root.innerHTML = value;
    }

    // Handle text changes
    quillRef.current.on('text-change', () => {
      if (isUpdatingRef.current) return;
      
      const html = quillRef.current.root.innerHTML;
      const isEmpty = quillRef.current.getText().trim().length === 0;
      onChange?.(isEmpty ? '' : html);
    });

    return () => {
      if (quillRef.current) {
        quillRef.current = null;
      }
    };
  }, []);

  // Update content when value prop changes
  useEffect(() => {
    if (!quillRef.current) return;
    
    const currentContent = quillRef.current.root.innerHTML;
    if (currentContent !== value) {
      isUpdatingRef.current = true;
      quillRef.current.root.innerHTML = value || '';
      isUpdatingRef.current = false;
    }
  }, [value]);

  // Update readOnly state
  useEffect(() => {
    if (quillRef.current) {
      quillRef.current.enable(!readOnly);
    }
  }, [readOnly]);

  return (
    <div className={cn('rich-text-editor', className)}>
      <div
        ref={editorRef}
        style={{ height }}
        className="border border-base-300 rounded-lg"
      />
    </div>
  );
});

RichTextEditor.displayName = 'RichTextEditor';
